import { describe, test, expect, beforeEach, afterEach } from '@jest/globals';
import {
  setupTestDatabase,
  prisma,
  createTestUser,
} from '../../setup/test-db-setup';
import { UsageResetService } from '../../../src/services/billing/usage-reset.service';

describe('Usage Reset Service', () => {
  setupTestDatabase();

  let usageResetService: UsageResetService;

  beforeEach(() => {
    usageResetService = new UsageResetService();
  });

  afterEach(() => {
    usageResetService.stop();
  });

  test('should reset usage for users from previous month', async () => {
    // Create users with usage from previous month
    const lastMonth = new Date();
    lastMonth.setMonth(lastMonth.getMonth() - 1);

    const user1 = await createTestUser({
      email: '<EMAIL>',
      currentMonthEmails: 25,
      lastUsageReset: lastMonth,
    });

    const user2 = await createTestUser({
      email: '<EMAIL>',
      currentMonthEmails: 40,
      lastUsageReset: lastMonth,
    });

    // Create a user with current month usage (should not be reset)
    const user3 = await createTestUser({
      email: '<EMAIL>',
      currentMonthEmails: 15,
      lastUsageReset: new Date(),
    });

    // Manually trigger reset check
    await (usageResetService as any).runResetCheck();

    // Check that users 1 and 2 were reset
    const resetUser1 = await prisma.user.findUnique({
      where: { id: user1.id },
      select: { currentMonthEmails: true, lastUsageReset: true }
    });
    expect(resetUser1?.currentMonthEmails).toBe(0);

    const resetUser2 = await prisma.user.findUnique({
      where: { id: user2.id },
      select: { currentMonthEmails: true, lastUsageReset: true }
    });
    expect(resetUser2?.currentMonthEmails).toBe(0);

    // Check that user 3 was not reset
    const notResetUser = await prisma.user.findUnique({
      where: { id: user3.id },
      select: { currentMonthEmails: true }
    });
    expect(notResetUser?.currentMonthEmails).toBe(15);
  });

  test('should not reset users with zero usage', async () => {
    const lastMonth = new Date();
    lastMonth.setMonth(lastMonth.getMonth() - 1);

    const userWithZeroUsage = await createTestUser({
      email: '<EMAIL>',
      currentMonthEmails: 0,
      lastUsageReset: lastMonth,
    });

    const initialUser = await prisma.user.findUnique({
      where: { id: userWithZeroUsage.id },
      select: { currentMonthEmails: true, lastUsageReset: true }
    });

    // Manually trigger reset check
    await (usageResetService as any).runResetCheck();

    // User should not be touched since they have zero usage
    const afterResetUser = await prisma.user.findUnique({
      where: { id: userWithZeroUsage.id },
      select: { currentMonthEmails: true, lastUsageReset: true }
    });

    expect(afterResetUser?.currentMonthEmails).toBe(0);
    expect(afterResetUser?.lastUsageReset).toEqual(initialUser?.lastUsageReset);
  });

  test('should manually reset specific user usage', async () => {
    const testUser = await createTestUser({
      email: '<EMAIL>',
      currentMonthEmails: 35,
    });

    const result = await usageResetService.resetUserUsage(testUser.id);
    expect(result.success).toBe(true);

    const resetUser = await prisma.user.findUnique({
      where: { id: testUser.id },
      select: { currentMonthEmails: true, lastUsageReset: true }
    });

    expect(resetUser?.currentMonthEmails).toBe(0);
    expect(resetUser?.lastUsageReset).toBeDefined();
  });

  test('should handle manual reset for non-existent user', async () => {
    const result = await usageResetService.resetUserUsage('non-existent-id');
    expect(result.success).toBe(false);
    expect(result.error).toBe('User not found');
  });

  test('should get reset statistics correctly', async () => {
    const lastMonth = new Date();
    lastMonth.setMonth(lastMonth.getMonth() - 1);

    // Create users with different states
    await createTestUser({
      email: '<EMAIL>',
      currentMonthEmails: 20,
      lastUsageReset: lastMonth, // Needs reset
    });

    await createTestUser({
      email: '<EMAIL>',
      currentMonthEmails: 0,
      lastUsageReset: lastMonth, // Has zero usage
    });

    await createTestUser({
      email: '<EMAIL>',
      currentMonthEmails: 15,
      lastUsageReset: new Date(), // Current month
    });

    const stats = await usageResetService.getResetStats();
    
    expect(stats.totalUsers).toBeGreaterThanOrEqual(3);
    expect(stats.usersWithUsage).toBeGreaterThanOrEqual(2); // stats-1 and stats-3
    expect(stats.usersNeedingReset).toBeGreaterThanOrEqual(1); // stats-1
    expect(stats.lastResetCheck).toBeInstanceOf(Date);
  });

  test('should handle users with very old lastUsageReset', async () => {
    // Create user with very old lastUsageReset (edge case)
    const veryOldDate = new Date('2020-01-01');
    const userWithOldReset = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: 'hashedpassword',
        currentMonthEmails: 10,
        monthlyEmailLimit: 50,
        lastUsageReset: veryOldDate,
        planType: 'free'
      }
    });

    // Manually trigger reset check
    await (usageResetService as any).runResetCheck();

    const resetUser = await prisma.user.findUnique({
      where: { id: userWithOldReset.id },
      select: { currentMonthEmails: true, lastUsageReset: true }
    });

    expect(resetUser?.currentMonthEmails).toBe(0);
    expect(resetUser?.lastUsageReset).toBeDefined();
    expect(new Date(resetUser?.lastUsageReset || 0).getTime()).toBeGreaterThan(veryOldDate.getTime());
  });

  test('should start and stop service correctly', () => {
    expect(() => {
      usageResetService.start();
      usageResetService.stop();
    }).not.toThrow();

    // Test starting already running service
    usageResetService.start();
    expect(() => {
      usageResetService.start(); // Should warn but not throw
    }).not.toThrow();
    usageResetService.stop();
  });

  test('should handle cross-month boundary correctly', async () => {
    // Create a date that's definitely from a different month/year
    const differentMonth = new Date('2023-01-15'); // Fixed past date
    
    const testUser = await createTestUser({
      email: '<EMAIL>',
      currentMonthEmails: 30,
      lastUsageReset: differentMonth,
    });

    // Manually trigger reset check
    await (usageResetService as any).runResetCheck();

    const resetUser = await prisma.user.findUnique({
      where: { id: testUser.id },
      select: { currentMonthEmails: true, lastUsageReset: true }
    });

    expect(resetUser?.currentMonthEmails).toBe(0);
    
    // lastUsageReset should be updated to current time
    const now = new Date();
    const resetTime = new Date(resetUser?.lastUsageReset || 0);
    expect(resetTime.getMonth()).toBe(now.getMonth());
    expect(resetTime.getFullYear()).toBe(now.getFullYear());
  });
});
